using MemoryPack;
using MongoDB.Bson;
using MongoDB.Bson.Serialization.Attributes;

namespace MaoYouJi
{
  /// <summary>
  /// 公会邀请信息 - 存储在数据库中的邀请记录
  /// </summary>
  [EnableClass]
  [MemoryPackable]
  public partial class GuildInviteInfo
  {
    [BsonId]
    public ObjectId Id { get; set; } // 唯一ID
    public long guildId { get; set; } // 公会ID
    public string guildName { get; set; } // 公会名称
    public long inviterId { get; set; } // 邀请者ID
    public string inviterName { get; set; } // 邀请者名称
    public long inviteeId { get; set; } // 被邀请者ID
    public string inviteeName { get; set; } // 被邀请者名称
    public long inviteTime { get; set; } // 邀请时间
    public string inviteMessage { get; set; } = ""; // 邀请消息
    public GuildInviteStatus status { get; set; } = GuildInviteStatus.Pending; // 邀请状态
    public long expireTime { get; set; } // 过期时间
  }

  /// <summary>
  /// 公会联盟邀请信息
  /// </summary>
  [EnableClass]
  [MemoryPackable]
  public partial class GuildAllianceInviteInfo
  {
    [BsonId]
    public ObjectId Id { get; set; } // 唯一ID
    public long fromGuildId { get; set; } // 发起方公会ID
    public string fromGuildName { get; set; } // 发起方公会名称
    public long toGuildId { get; set; } // 目标公会ID
    public string toGuildName { get; set; } // 目标公会名称
    public long inviterId { get; set; } // 邀请者ID
    public string inviterName { get; set; } // 邀请者名称
    public long inviteTime { get; set; } // 邀请时间
    public string inviteMessage { get; set; } = ""; // 邀请消息
    public GuildInviteStatus status { get; set; } = GuildInviteStatus.Pending; // 邀请状态
    public long expireTime { get; set; } // 过期时间
  }
}
