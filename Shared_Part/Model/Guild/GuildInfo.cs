using System.Collections.Concurrent;
using System.Threading;
using MemoryPack;
using MongoDB.Bson.Serialization.Attributes;
using MongoDB.Bson.Serialization.Options;

namespace MaoYouJi
{
  /// <summary>
  /// 公会成员信息
  /// </summary>
  [EnableClass]
  [MemoryPackable]
  public partial class GuildMemberInfo
  {
    public long userId { get; set; } // 用户ID
    public string name { get; set; } // 用户名称
    public GuildRole role { get; set; } = GuildRole.Member; // 公会角色
    public long joinTime { get; set; } // 加入时间
    public int level { get; set; } // 等级
    public long attackNum { get; set; } // 战斗力
    public OfflineStateEnum offlineState { get; set; } // 在线状态
    public long lastActiveTime { get; set; } // 最后活跃时间
    public string contribution { get; set; } = ""; // 贡献度描述
  }

  /// <summary>
  /// 公会联盟信息
  /// </summary>
  [EnableClass]
  [MemoryPackable]
  public partial class GuildAllianceInfo
  {
    public long guildId { get; set; } // 联盟公会ID
    public string guildName { get; set; } // 联盟公会名称
    public long establishTime { get; set; } // 建立联盟时间
    public GuildAllianceStatus status { get; set; } = GuildAllianceStatus.Established; // 联盟状态
  }

  /// <summary>
  /// 公会基础信息 - 继承Entity实现ECS架构
  /// </summary>
  [ChildOf]
  public partial class GuildInfo : Entity, IAwake, IAwake<string, User>, IDestroy
  {
    public string name { get; set; } = ""; // 公会名称
    public string description { get; set; } = ""; // 公会描述
    public string announcement { get; set; } = ""; // 公会公告
    public long leaderId { get; set; } = 0; // 会长ID
    public int level { get; set; } = 1; // 公会等级
    public int maxMembers { get; set; } = 50; // 最大成员数
    public long createTime { get; set; } = 0; // 创建时间
    public long updateTime { get; set; } = 0; // 更新时间
    public long lastUpdateTime { get; set; } = 0; // 最后更新时间戳（用于数据库自动保存判断）

    // 成员信息字典 - 使用MongoDB数组序列化
    [BsonDictionaryOptions(DictionaryRepresentation.ArrayOfArrays)]
    public ConcurrentDictionary<long, GuildMemberInfo> memberInfos { get; set; } = new();

    // 联盟信息字典
    [BsonDictionaryOptions(DictionaryRepresentation.ArrayOfArrays)]
    public ConcurrentDictionary<long, GuildAllianceInfo> allianceInfos { get; set; } = new();

    // 读写锁保护成员信息
    [MemoryPackIgnore]
    [BsonIgnore]
    public ReaderWriterLockSlim memberInfosLock { get; } = new ReaderWriterLockSlim();

    // 读写锁保护联盟信息
    [MemoryPackIgnore]
    [BsonIgnore]
    public ReaderWriterLockSlim allianceInfosLock { get; } = new ReaderWriterLockSlim();
  }
}
