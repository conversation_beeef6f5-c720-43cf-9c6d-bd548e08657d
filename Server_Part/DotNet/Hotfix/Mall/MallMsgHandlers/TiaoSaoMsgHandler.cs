namespace MaoYouJi
{
  [MessageLocationHandler(SceneType.Map)]
  public class ClientGetTiaoSaoShopMsgHandler : MessageLocationHandler<MapNode, ClientGetTiaoSaoShopMsg>
  {
    protected override async ETTask Run(MapNode nowMap, ClientGetTiaoSaoShopMsg msg)
    {
      LogicRet logicRet = nowMap.GetUserWithCheck(msg.UserId, out User user);
      if (!logicRet.IsSuccess)
      {
        user.SendToast(logicRet.Message);
        return;
      }
      TiaoSaoManageComp tiaoSaoManageComp = nowMap.Root().GetComponent<TiaoSaoManageComp>();
      ServerShowTiaoSaoShopMsg serverShowTiaoSaoShopMsg = new ServerShowTiaoSaoShopMsg
      {
        TiaoSaoShopList = await tiaoSaoManageComp.GetTiaoSaoShopList(msg),
        totalNum = await tiaoSaoManageComp.GetTiaoSaoShopInfoListCount(msg),
        page = msg.page,
        isMyShop = msg.isMyShop,
      };
      ETLog.Info($"ServerShowTiaoSaoShopMsg: {serverShowTiaoSaoShopMsg.TiaoSaoShopList.Count}");
      user.SendMessage(serverShowTiaoSaoShopMsg);
    }
  }

  [MessageLocationHandler(SceneType.Map)]
  public class ClientUploadTiaoSaoShopMsgHandler : MessageLocationHandler<MapNode, ClientUploadTiaoSaoShopMsg>
  {
    protected override async ETTask Run(MapNode nowMap, ClientUploadTiaoSaoShopMsg msg)
    {
      LogicRet logicRet = nowMap.GetUserWithCheck(msg.UserId, out User user);
      if (!logicRet.IsSuccess)
      {
        user.SendToast(logicRet.Message);
        return;
      }
      BagComponent bagComponent = user.GetComponent<BagComponent>();
      Thing thing = bagComponent.GetThingInBag<Thing>(msg.thingId);
      if (thing == null)
      {
        user.SendToast("物品不存在");
        return;
      }
      if (thing.ownType != OwnType.PUBLIC)
      {
        user.SendToast("该物品无法交易");
        return;
      }
      if (thing.thingSubType == ThingSubType.Bag)
      {
        Treasure bag = (Treasure)thing;
        if (bag.thingsInBag.Count > 0)
        {
          user.SendToast("背包中还有物品");
          return;
        }
      }
      if (thing.thingType == ThingType.EQUIP)
      {
        Equipment equip = (Equipment)thing;
        foreach (Thing gem in equip.gemList)
        {
          if (gem != null)
          {
            user.SendToast("装备镶嵌了宝石");
            return;
          }
        }
      }
      if (msg.upNum <= 0)
      {
        user.SendToast("上架数量不能小于等于0");
        return;
      }
      if (msg.price <= 0)
      {
        user.SendToast("价格不能小于等于0");
        return;
      }
      if (msg.upDay <= 0)
      {
        user.SendToast("上架天数不能小于等于0");
        return;
      }
      if (thing.num < msg.upNum)
      {
        user.SendToast("物品数量不足");
        return;
      }
      TiaoSaoManageComp tiaoSaoManageComp = nowMap.Root().GetComponent<TiaoSaoManageComp>();
      bagComponent.AddThingNumWithSend(thing, -msg.upNum);
      ETLog.Info($"upTiaoSaoShop: {user.Id}, {thing.thingName}, {thing.grade}, {thing.ownType}, {msg.upNum}, {msg.price}, {msg.upDay}");
      TiaoSaoSellUserInfo tiaoSaoSellUserInfo = new TiaoSaoSellUserInfo();
      tiaoSaoSellUserInfo.userId = user.Id;
      tiaoSaoSellUserInfo.username = user.nickname;
      TiaoSaoShopInfo tiaoSaoShopInfo = new TiaoSaoShopInfo();
      tiaoSaoShopInfo.shopName = thing.name;
      tiaoSaoShopInfo.thing = thing.Clone() as Thing;
      tiaoSaoShopInfo.thing.num = msg.upNum;
      tiaoSaoShopInfo.price = msg.price;
      tiaoSaoShopInfo.sellUserInfo = tiaoSaoSellUserInfo;
      tiaoSaoShopInfo.uploadTime = TimeInfo.Instance.ServerNow();
      tiaoSaoShopInfo.endTime = tiaoSaoShopInfo.uploadTime + msg.upDay * 24 * 60 * 60 * 1000;
      await tiaoSaoManageComp.AddTiaoSaoShopInfo(tiaoSaoShopInfo);
      user.SendToast("上架成功");
    }
  }

  [MessageLocationHandler(SceneType.Map)]
  public class ClientDeleteTiaoSaoShopMsgHandler : MessageLocationHandler<MapNode, ClientDeleteTiaoSaoShopMsg>
  {
    protected override async ETTask Run(MapNode nowMap, ClientDeleteTiaoSaoShopMsg msg)
    {
      LogicRet logicRet = nowMap.GetUserWithCheck(msg.UserId, out User user);
      if (!logicRet.IsSuccess)
      {
        user.SendToast(logicRet.Message);
        return;
      }
      if (msg.tiaoSaoShopId == null)
      {
        user.SendToast("下架商品不能为空");
        return;
      }
      TiaoSaoManageComp tiaoSaoManageComp = nowMap.Root().GetComponent<TiaoSaoManageComp>();
      TiaoSaoShopInfo tiaoSaoShopInfo = await tiaoSaoManageComp.GetTiaoSaoShopInfo(msg.tiaoSaoShopId);
      if (tiaoSaoShopInfo == null)
      {
        user.SendToast("商品不存在");
        return;
      }
      if (tiaoSaoShopInfo.sellUserInfo.userId != user.Id)
      {
        user.SendToast("只能下架自己的商品");
        return;
      }
      bool isSuccess = await tiaoSaoManageComp.DeleteTiaoSaoShopInfo(msg.tiaoSaoShopId);
      if (!isSuccess)
      {
        user.SendToast("下架失败");
        return;
      }
      user.SendToast("下架成功");
    }
  }

  [MessageLocationHandler(SceneType.Map)]
  public class ClientBuyTiaoSaoShopMsgHandler : MessageLocationHandler<MapNode, ClientBuyTiaoSaoShopMsg>
  {
    protected override async ETTask Run(MapNode nowMap, ClientBuyTiaoSaoShopMsg msg)
    {
      LogicRet logicRet = nowMap.GetUserWithCheck(msg.UserId, out User user);
      if (!logicRet.IsSuccess)
      {
        user.SendToast(logicRet.Message);
        return;
      }
      TiaoSaoManageComp tiaoSaoManageComp = nowMap.Root().GetComponent<TiaoSaoManageComp>();
      TiaoSaoShopInfo tiaoSaoShopInfo = await tiaoSaoManageComp.GetTiaoSaoShopInfo(msg.tiaoSaoShopId);
      if (tiaoSaoShopInfo == null)
      {
        user.SendToast("商品不存在");
        return;
      }
      if (msg.num <= 0)
      {
        user.SendToast("购买数量不能小于等于0");
        return;
      }
      if (tiaoSaoShopInfo.thing.num < msg.num)
      {
        user.SendToast("商品数量不足");
        return;
      }
      if (tiaoSaoShopInfo.sellUserInfo.userId == user.Id)
      {
        user.SendToast("不能购买自己上架的商品");
        return;
      }
      BagComponent bagComponent = user.GetComponent<BagComponent>();
      long totalPrice = tiaoSaoShopInfo.price * msg.num;
      if (bagComponent.coin < totalPrice)
      {
        user.SendToast("金币不足");
        return;
      }
      tiaoSaoShopInfo = await tiaoSaoManageComp.SubTiaoSaoShopInfoNum(msg.tiaoSaoShopId, msg.num);
      if (tiaoSaoShopInfo == null)
      {
        user.SendToast("商品数量不足");
        return;
      }
      if (tiaoSaoShopInfo.thing.num <= 0)
      {
        await tiaoSaoManageComp.DeleteTiaoSaoShopInfo(msg.tiaoSaoShopId);
      }
      ChatManageComp chatManageComp = nowMap.Root().GetComponent<ChatManageComp>();
      bagComponent.AddAllCoinWithSend(-totalPrice);
      Thing needGiveThing = tiaoSaoShopInfo.thing;
      needGiveThing.num = msg.num;
      await chatManageComp.SendMail(user.Id, "跳蚤商品购买成功", "您购买了" + tiaoSaoShopInfo.shopName + "x" + msg.num, 0, 0, 0, [needGiveThing]);
      await chatManageComp.SendMail(tiaoSaoShopInfo.sellUserInfo.userId, "跳蚤商品卖出收益",
          "您卖出了" + tiaoSaoShopInfo.shopName + "x" + msg.num + "，获得" + ChatProSystem.GetCoinNumStr(totalPrice), totalPrice,
          0, 0, null);
      user.SendToast("购买成功");
    }
  }
}