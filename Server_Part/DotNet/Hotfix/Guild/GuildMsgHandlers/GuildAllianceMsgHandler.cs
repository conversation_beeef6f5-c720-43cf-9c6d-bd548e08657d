using MongoDB.Driver;

namespace MaoYouJi
{
  /// <summary>
  /// 创建公会联盟处理器
  /// </summary>
  [MessageLocationHandler(SceneType.Map)]
  [FriendOf(typeof(MapNode))]
  public class CreateGuildAllianceHandler : MessageLocationHandler<MapNode, CreateGuildAllianceReq, CreateGuildAllianceResp>
  {
    protected override async ETTask Run(MapNode nowMap, CreateGuildAllianceReq request, CreateGuildAllianceResp response)
    {
      LogicRet getUserGuildRet = GuildProcSys.GetUserGuildWithCheck(request.UserId, out GuildInfo guildInfo, out User user);
      if (!getUserGuildRet.IsSuccess)
      {
        response.SetError(getUserGuildRet.Message);
        return;
      }

      // 检查联盟权限
      LogicRet permissionRet = guildInfo.CheckAlliancePermission(user.Id);
      if (!permissionRet.IsSuccess)
      {
        response.SetError(permissionRet.Message);
        return;
      }

      // 查找目标公会
      LogicRet findGuildRet = GuildProcSys.FindGuildByIdOrName(request.targetGuildId, request.targetGuildName, out GuildInfo targetGuild);
      if (!findGuildRet.IsSuccess)
      {
        response.SetError(findGuildRet.Message);
        return;
      }

      // 检查是否是同一个公会
      if (guildInfo.Id == targetGuild.Id)
      {
        response.SetError("不能与自己的公会结盟");
        return;
      }

      // 检查是否已经结盟
      if (guildInfo.allianceInfos.ContainsKey(targetGuild.Id))
      {
        response.SetError("已与该公会结盟");
        return;
      }

      // 验证邀请消息
      LogicRet validateMsgRet = GuildProcSys.ValidateInviteMessage(request.inviteMessage);
      if (!validateMsgRet.IsSuccess)
      {
        response.SetError(validateMsgRet.Message);
        return;
      }

      // 添加联盟邀请信息
      GuildManageComponent guildManageComponent = nowMap.Root().GetComponent<GuildManageComponent>();
      guildManageComponent.AddGuildAllianceInviteInfo(guildInfo, targetGuild, user, request.inviteMessage);

      user.SendChat($"已向公会 {targetGuild.name} 发送联盟邀请");
      GuildProcSys.LogGuildOperation(guildInfo, user, "发送联盟邀请", $"目标公会: {targetGuild.name}");

      await ETTask.CompletedTask;
    }
  }

  /// <summary>
  /// 接受公会联盟处理器
  /// </summary>
  [MessageLocationHandler(SceneType.Map)]
  [FriendOf(typeof(MapNode))]
  public class AcceptGuildAllianceHandler : MessageLocationHandler<MapNode, AcceptGuildAllianceReq, AcceptGuildAllianceResp>
  {
    protected override async ETTask Run(MapNode nowMap, AcceptGuildAllianceReq request, AcceptGuildAllianceResp response)
    {
      LogicRet getUserGuildRet = GuildProcSys.GetUserGuildWithCheck(request.UserId, out GuildInfo guildInfo, out User user);
      if (!getUserGuildRet.IsSuccess)
      {
        response.SetError(getUserGuildRet.Message);
        return;
      }

      // 检查联盟权限（只有会长可以接受联盟）
      LogicRet permissionRet = guildInfo.CheckPermission(user.Id, GuildRole.President);
      if (!permissionRet.IsSuccess)
      {
        response.SetError("只有会长可以接受联盟邀请");
        return;
      }

      // 查找联盟邀请信息
      var collection = nowMap.Root().GetComponent<DBManagerComponent>().GetMyZoneDB().GetCollection<GuildAllianceInviteInfo>();
      var filter = MongoDB.Driver.Builders<GuildAllianceInviteInfo>.Filter.And(
        MongoDB.Driver.Builders<GuildAllianceInviteInfo>.Filter.Eq(x => x.Id, request.inviteId),
        MongoDB.Driver.Builders<GuildAllianceInviteInfo>.Filter.Eq(x => x.toGuildId, guildInfo.Id),
        MongoDB.Driver.Builders<GuildAllianceInviteInfo>.Filter.Eq(x => x.status, GuildInviteStatus.Pending)
      );

      var allianceInviteInfo = await collection.Find(filter).FirstOrDefaultAsync();
      if (allianceInviteInfo == null)
      {
        response.SetError("联盟邀请不存在或已过期");
        return;
      }

      // 检查邀请是否过期
      if (TimeInfo.Instance.ServerNow() > allianceInviteInfo.expireTime)
      {
        // 更新状态为过期
        var updateFilter = MongoDB.Driver.Builders<GuildAllianceInviteInfo>.Filter.Eq(x => x.Id, request.inviteId);
        var update = MongoDB.Driver.Builders<GuildAllianceInviteInfo>.Update.Set(x => x.status, GuildInviteStatus.Expired);
        await collection.UpdateOneAsync(updateFilter, update);
        response.SetError("联盟邀请已过期");
        return;
      }

      // 获取发起方公会
      GuildInfo fromGuild = GlobalInfoCache.Instance.GetGuild(allianceInviteInfo.fromGuildId);
      if (fromGuild == null)
      {
        response.SetError("发起方公会不存在");
        return;
      }

      // 检查是否已经结盟
      if (guildInfo.allianceInfos.ContainsKey(fromGuild.Id))
      {
        response.SetError("已与该公会结盟");
        return;
      }

      // 建立联盟
      LogicRet addAllianceRet = guildInfo.AddAlliance(fromGuild);
      if (!addAllianceRet.IsSuccess)
      {
        response.SetError(addAllianceRet.Message);
        return;
      }

      // 更新邀请状态
      var statusUpdate = MongoDB.Driver.Builders<GuildAllianceInviteInfo>.Update.Set(x => x.status, GuildInviteStatus.Accepted);
      await collection.UpdateOneAsync(filter, statusUpdate);

      user.SendChat($"已与公会 {fromGuild.name} 建立联盟");
      GuildProcSys.LogGuildOperation(guildInfo, user, "接受联盟邀请", $"联盟公会: {fromGuild.name}");

      await ETTask.CompletedTask;
    }
  }

  /// <summary>
  /// 拒绝公会联盟处理器
  /// </summary>
  [MessageLocationHandler(SceneType.Map)]
  [FriendOf(typeof(MapNode))]
  public class RejectGuildAllianceHandler : MessageLocationHandler<MapNode, RejectGuildAllianceReq, RejectGuildAllianceResp>
  {
    protected override async ETTask Run(MapNode nowMap, RejectGuildAllianceReq request, RejectGuildAllianceResp response)
    {
      LogicRet getUserGuildRet = GuildProcSys.GetUserGuildWithCheck(request.UserId, out GuildInfo guildInfo, out User user);
      if (!getUserGuildRet.IsSuccess)
      {
        response.SetError(getUserGuildRet.Message);
        return;
      }

      // 检查权限（只有会长可以拒绝联盟）
      LogicRet permissionRet = guildInfo.CheckPermission(user.Id, GuildRole.President);
      if (!permissionRet.IsSuccess)
      {
        response.SetError("只有会长可以拒绝联盟邀请");
        return;
      }

      // 查找并更新联盟邀请信息
      var collection = nowMap.Root().GetComponent<DBManagerComponent>().GetMyZoneDB().GetCollection<GuildAllianceInviteInfo>();
      var filter = MongoDB.Driver.Builders<GuildAllianceInviteInfo>.Filter.And(
        MongoDB.Driver.Builders<GuildAllianceInviteInfo>.Filter.Eq(x => x.Id, request.inviteId),
        MongoDB.Driver.Builders<GuildAllianceInviteInfo>.Filter.Eq(x => x.toGuildId, guildInfo.Id),
        MongoDB.Driver.Builders<GuildAllianceInviteInfo>.Filter.Eq(x => x.status, GuildInviteStatus.Pending)
      );

      var allianceInviteInfo = await collection.Find(filter).FirstOrDefaultAsync();
      if (allianceInviteInfo == null)
      {
        response.SetError("联盟邀请不存在或已过期");
        return;
      }

      // 更新邀请状态
      var statusUpdate = MongoDB.Driver.Builders<GuildAllianceInviteInfo>.Update.Set(x => x.status, GuildInviteStatus.Rejected);
      await collection.UpdateOneAsync(filter, statusUpdate);

      user.SendChat("已拒绝联盟邀请");
      GuildProcSys.LogGuildOperation(guildInfo, user, "拒绝联盟邀请", $"发起方公会: {allianceInviteInfo.fromGuildName}");

      await ETTask.CompletedTask;
    }
  }

  /// <summary>
  /// 解除公会联盟处理器
  /// </summary>
  [MessageLocationHandler(SceneType.Map)]
  [FriendOf(typeof(MapNode))]
  public class RemoveGuildAllianceHandler : MessageLocationHandler<MapNode, RemoveGuildAllianceReq, RemoveGuildAllianceResp>
  {
    protected override async ETTask Run(MapNode nowMap, RemoveGuildAllianceReq request, RemoveGuildAllianceResp response)
    {
      LogicRet getUserGuildRet = GuildProcSys.GetUserGuildWithCheck(request.UserId, out GuildInfo guildInfo, out User user);
      if (!getUserGuildRet.IsSuccess)
      {
        response.SetError(getUserGuildRet.Message);
        return;
      }

      // 检查解除联盟权限（只有会长可以解除联盟）
      LogicRet permissionRet = guildInfo.CheckRemoveAlliancePermission(user.Id);
      if (!permissionRet.IsSuccess)
      {
        response.SetError(permissionRet.Message);
        return;
      }

      // 检查是否存在联盟关系
      if (!guildInfo.allianceInfos.TryGetValue(request.targetGuildId, out GuildAllianceInfo allianceInfo))
      {
        response.SetError("未与该公会结盟");
        return;
      }

      // 解除联盟
      LogicRet removeAllianceRet = guildInfo.RemoveAlliance(request.targetGuildId);
      if (!removeAllianceRet.IsSuccess)
      {
        response.SetError(removeAllianceRet.Message);
        return;
      }

      user.SendChat($"已与公会 {allianceInfo.guildName} 解除联盟");
      GuildProcSys.LogGuildOperation(guildInfo, user, "解除联盟", $"目标公会: {allianceInfo.guildName}");

      await ETTask.CompletedTask;
    }
  }
}
