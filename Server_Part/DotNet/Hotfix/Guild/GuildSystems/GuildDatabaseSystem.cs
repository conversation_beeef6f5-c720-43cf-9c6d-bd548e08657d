using System;

namespace MaoYouJi
{
  /// <summary>
  /// 公会数据库操作系统
  /// </summary>
  public static class GuildDatabaseSystem
  {
    /// <summary>
    /// 将公会信息插入到数据库
    /// </summary>
    public static async ETTask InsertGuildToDatabase(this GuildInfo guildInfo)
    {
      try
      {
        // 更新最后更新时间
        guildInfo.UpdateLastUpdateTime();
        
        // 获取数据库组件
        DBComponent dbComponent = guildInfo.Scene().GetComponent<DBManagerComponent>().GetMyZoneDB();
        
        // 插入到数据库
        await dbComponent.Save(guildInfo);
        
        ETLog.Info($"公会 {guildInfo.name}(ID: {guildInfo.Id}) 已成功插入到数据库");
      }
      catch (Exception ex)
      {
        ETLog.Error($"插入公会到数据库失败: {guildInfo.name}(ID: {guildInfo.Id}), 错误: {ex.Message}");
        throw;
      }
    }

    /// <summary>
    /// 从数据库删除公会信息
    /// </summary>
    public static async ETTask DeleteGuildFromDatabase(this GuildInfo guildInfo)
    {
      try
      {
        // 获取数据库组件
        DBComponent dbComponent = guildInfo.Scene().GetComponent<DBManagerComponent>().GetMyZoneDB();
        
        // 从数据库删除
        await dbComponent.Remove<GuildInfo>(guildInfo.Id);
        
        ETLog.Info($"公会 {guildInfo.name}(ID: {guildInfo.Id}) 已从数据库删除");
      }
      catch (Exception ex)
      {
        ETLog.Error($"从数据库删除公会失败: {guildInfo.name}(ID: {guildInfo.Id}), 错误: {ex.Message}");
        throw;
      }
    }

    /// <summary>
    /// 更新公会的最后更新时间戳
    /// </summary>
    public static void UpdateLastUpdateTime(this GuildInfo guildInfo)
    {
      long now = TimeInfo.Instance.ServerNow();
      guildInfo.lastUpdateTime = now;
      guildInfo.updateTime = now; // 同时更新原有的updateTime字段保持兼容性
    }

    /// <summary>
    /// 保存公会信息到数据库（用于更新操作）
    /// </summary>
    public static async ETTask SaveGuildToDatabase(this GuildInfo guildInfo)
    {
      try
      {
        // 更新最后更新时间
        guildInfo.UpdateLastUpdateTime();
        
        // 获取数据库组件
        DBComponent dbComponent = guildInfo.Scene().GetComponent<DBManagerComponent>().GetMyZoneDB();
        
        // 保存到数据库
        await dbComponent.Save(guildInfo);
        
        ETLog.Debug($"公会 {guildInfo.name}(ID: {guildInfo.Id}) 信息已保存到数据库");
      }
      catch (Exception ex)
      {
        ETLog.Error($"保存公会到数据库失败: {guildInfo.name}(ID: {guildInfo.Id}), 错误: {ex.Message}");
        throw;
      }
    }

    /// <summary>
    /// 从数据库加载公会信息
    /// </summary>
    public static async ETTask<GuildInfo> LoadGuildFromDatabase(Scene scene, long guildId)
    {
      try
      {
        // 获取数据库组件
        DBComponent dbComponent = scene.GetComponent<DBManagerComponent>().GetMyZoneDB();
        
        // 从数据库查询
        GuildInfo guildInfo = await dbComponent.Query<GuildInfo>(guildId);
        
        if (guildInfo != null)
        {
          ETLog.Debug($"从数据库加载公会: {guildInfo.name}(ID: {guildInfo.Id})");
        }
        
        return guildInfo;
      }
      catch (Exception ex)
      {
        ETLog.Error($"从数据库加载公会失败: ID {guildId}, 错误: {ex.Message}");
        return null;
      }
    }

    /// <summary>
    /// 检查公会是否需要保存到数据库（基于更新时间）
    /// </summary>
    public static bool NeedsSaveToDatabase(this GuildInfo guildInfo, long lastSaveTime)
    {
      return guildInfo.lastUpdateTime > lastSaveTime;
    }
  }
}
