using System.Collections.Generic;
using MongoDB.Bson;
using MongoDB.Driver;

namespace MaoYouJi
{
  /// <summary>
  /// 公会管理组件系统
  /// </summary>
  [EntitySystemOf(typeof(GuildManageComponent))]
  [FriendOf(typeof(GuildManageComponent))]
  public static partial class GuildManageCompSystem
  {
    [EntitySystem]
    private static void Awake(this GuildManageComponent self)
    {
    }

    /// <summary>
    /// 创建公会
    /// </summary>
    public static async ETTask<GuildInfo> CreateGuild(this GuildManageComponent self, string guildName, string description, User creator)
    {
      // 创建公会实体
      GuildInfo guildInfo = self.AddChild<GuildInfo, string, User>(guildName, creator);

      if (!string.IsNullOrEmpty(description))
      {
        guildInfo.description = description;
      }

      // 更新用户公会信息
      creator.userComInfo = new UserComInfo
      {
        commId = guildInfo.Id,
        commName = guildInfo.name
      };

      // 添加到全局缓存
      GlobalInfoCache.Instance.AddGuild(guildInfo.Id, guildInfo);

      try
      {
        // 立即将公会信息插入到数据库
        await guildInfo.InsertGuildToDatabase();
        ETLog.Info($"创建公会成功: {guildInfo.name}, ID: {guildInfo.Id}, 创建者: {creator.nickname}");
      }
      catch (Exception ex)
      {
        // 如果数据库插入失败，回滚操作
        ETLog.Error($"创建公会时数据库插入失败，正在回滚: {ex.Message}");

        // 从全局缓存中移除
        GlobalInfoCache.Instance.RemoveGuild(guildInfo.Id);

        // 清理用户公会信息
        creator.userComInfo = null;

        // 销毁实体
        guildInfo.Dispose();

        throw new Exception($"创建公会失败：数据库操作异常 - {ex.Message}");
      }

      return guildInfo;
    }

    /// <summary>
    /// 解散公会
    /// </summary>
    public static async ETTask DisbandGuild(this GuildManageComponent self, GuildInfo guildInfo)
    {
      string guildName = guildInfo.name;
      long guildId = guildInfo.Id;

      try
      {
        // 先从数据库删除公会信息
        await guildInfo.DeleteGuildFromDatabase();
      }
      catch (Exception ex)
      {
        ETLog.Error($"解散公会时数据库删除失败: {guildName}(ID: {guildId}), 错误: {ex.Message}");
        // 数据库删除失败时仍然继续执行内存清理，但记录错误
      }

      // 清理所有成员的公会信息
      foreach (var memberKvp in guildInfo.memberInfos)
      {
        User member = GlobalInfoCache.Instance.GetOnlineUser(memberKvp.Key);
        if (member != null)
        {
          member.userComInfo = null;
          member.SendMessage(new ServerGuildDisbandMsg
          {
            guildId = guildId,
            guildName = guildName,
            disbandMessage = $"公会 {guildName} 已被解散"
          });
        }
      }

      // 从全局缓存中移除
      GlobalInfoCache.Instance.RemoveGuild(guildId);

      // 销毁实体
      guildInfo.Dispose();

      ETLog.Info($"解散公会: {guildName}, ID: {guildId}");
    }

    /// <summary>
    /// 添加公会邀请信息到数据库
    /// </summary>
    public static void AddGuildInviteInfo(this GuildManageComponent self, GuildInfo guildInfo, User inviter, User invitee, string inviteMessage)
    {
      var collection = self.Scene().GetComponent<DBManagerComponent>().GetMyZoneDB().GetCollection<GuildInviteInfo>();

      long now = TimeInfo.Instance.ServerNow();
      var guildInviteInfo = new GuildInviteInfo
      {
        guildId = guildInfo.Id,
        guildName = guildInfo.name,
        inviterId = inviter.Id,
        inviterName = inviter.nickname,
        inviteeId = invitee.Id,
        inviteeName = invitee.nickname,
        inviteTime = now,
        inviteMessage = inviteMessage,
        status = GuildInviteStatus.Pending,
        expireTime = now + GuildManageComponent.GUILD_INVITE_EXPIRE_TIME
      };

      collection.InsertOne(guildInviteInfo);

      // 发送邀请消息给被邀请者
      invitee.SendMessage(new ServerGuildInviteMsg
      {
        inviteInfo = new GuildInviteDaoInfo
        {
          guildDaoInfo = guildInfo.ToDaoInfo(),
          inviteMessage = inviteMessage,
          inviterName = inviter.nickname,
          inviteTime = now,
          expireTime = guildInviteInfo.expireTime
        }
      });
    }

    /// <summary>
    /// 处理公会邀请响应
    /// </summary>
    public static async ETTask<bool> ProcessGuildInviteResponse(this GuildManageComponent self, ObjectId inviteId, bool isAccept, User user)
    {
      var collection = self.Scene().GetComponent<DBManagerComponent>().GetMyZoneDB().GetCollection<GuildInviteInfo>();

      // 查找邀请信息
      var filter = Builders<GuildInviteInfo>.Filter.And(
        Builders<GuildInviteInfo>.Filter.Eq(x => x.Id, inviteId),
        Builders<GuildInviteInfo>.Filter.Eq(x => x.inviteeId, user.Id),
        Builders<GuildInviteInfo>.Filter.Eq(x => x.status, GuildInviteStatus.Pending)
      );

      var inviteInfo = await collection.Find(filter).FirstOrDefaultAsync();
      if (inviteInfo == null)
      {
        return false;
      }

      // 检查邀请是否过期
      if (TimeInfo.Instance.ServerNow() > inviteInfo.expireTime)
      {
        // 更新状态为过期
        var updateFilter = Builders<GuildInviteInfo>.Filter.Eq(x => x.Id, inviteId);
        var update = Builders<GuildInviteInfo>.Update.Set(x => x.status, GuildInviteStatus.Expired);
        await collection.UpdateOneAsync(updateFilter, update);
        return false;
      }

      // 更新邀请状态
      var statusUpdate = Builders<GuildInviteInfo>.Update.Set(x => x.status,
        isAccept ? GuildInviteStatus.Accepted : GuildInviteStatus.Rejected);
      await collection.UpdateOneAsync(filter, statusUpdate);

      return true;
    }

    /// <summary>
    /// 添加公会联盟邀请信息
    /// </summary>
    public static void AddGuildAllianceInviteInfo(this GuildManageComponent self, GuildInfo fromGuild, GuildInfo toGuild, User inviter, string inviteMessage)
    {
      var collection = self.Scene().GetComponent<DBManagerComponent>().GetMyZoneDB().GetCollection<GuildAllianceInviteInfo>();

      long now = TimeInfo.Instance.ServerNow();
      var allianceInviteInfo = new GuildAllianceInviteInfo
      {
        fromGuildId = fromGuild.Id,
        fromGuildName = fromGuild.name,
        toGuildId = toGuild.Id,
        toGuildName = toGuild.name,
        inviterId = inviter.Id,
        inviterName = inviter.nickname,
        inviteTime = now,
        inviteMessage = inviteMessage,
        status = GuildInviteStatus.Pending,
        expireTime = now + GuildManageComponent.GUILD_ALLIANCE_INVITE_EXPIRE_TIME
      };

      collection.InsertOne(allianceInviteInfo);

      // 通知目标公会的会长
      User targetLeader = GlobalInfoCache.Instance.GetOnlineUser(toGuild.leaderId);
      targetLeader?.SendMessage(new ServerGuildAllianceInviteMsg
      {
        allianceInviteInfo = new GuildAllianceInviteDaoInfo
        {
          fromGuildInfo = fromGuild.ToDaoInfo(),
          toGuildInfo = toGuild.ToDaoInfo(),
          inviteMessage = inviteMessage,
          inviterName = inviter.nickname,
          inviteTime = now,
          expireTime = allianceInviteInfo.expireTime
        }
      });
    }

    /// <summary>
    /// 检查公会名称是否合规
    /// </summary>
    public static LogicRet ValidateGuildName(this GuildManageComponent self, string guildName)
    {
      if (string.IsNullOrEmpty(guildName))
      {
        return LogicRet.Failed("公会名称不能为空");
      }

      if (guildName.Length < GuildManageComponent.GUILD_NAME_MIN_LENGTH)
      {
        return LogicRet.Failed($"公会名称长度不能少于{GuildManageComponent.GUILD_NAME_MIN_LENGTH}个字符");
      }

      if (guildName.Length > GuildManageComponent.GUILD_NAME_MAX_LENGTH)
      {
        return LogicRet.Failed($"公会名称长度不能超过{GuildManageComponent.GUILD_NAME_MAX_LENGTH}个字符");
      }

      // 检查是否包含特殊字符
      if (guildName.Contains(" ") || guildName.Contains("\t") || guildName.Contains("\n"))
      {
        return LogicRet.Failed("公会名称不能包含空格或特殊字符");
      }

      // 检查名称是否已存在
      if (GlobalInfoCache.Instance.IsGuildNameExists(guildName))
      {
        return LogicRet.Failed("公会名称已存在");
      }

      return LogicRet.Success;
    }

    /// <summary>
    /// 发送消息给公会所有成员
    /// </summary>
    public static void SendMessageToAllMembers(this GuildInfo guildInfo, params MaoYouMessage[] messages)
    {
      foreach (var memberKvp in guildInfo.memberInfos)
      {
        User member = GlobalInfoCache.Instance.GetOnlineUser(memberKvp.Key);
        member?.SendMessage(messages);
      }
    }
  }
}
