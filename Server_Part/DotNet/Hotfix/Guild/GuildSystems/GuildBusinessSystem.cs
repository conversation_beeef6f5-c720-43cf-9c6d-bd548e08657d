using System.Linq;

namespace MaoYouJi
{
  /// <summary>
  /// 公会业务逻辑系统
  /// </summary>
  [FriendOf(typeof(GuildInfo))]
  public static class GuildBusinessSystem
  {
    /// <summary>
    /// 添加公会成员
    /// </summary>
    public static LogicRet AddMember(this GuildInfo self, User user)
    {
      if (self.IsMember(user.Id))
      {
        return LogicRet.Failed("用户已是公会成员");
      }

      if (self.IsMaxMembers())
      {
        return LogicRet.Failed("公会成员已满");
      }

      self.memberInfosLock.EnterWriteLock();
      try
      {
        // 创建成员信息
        AttackComponent attackComponent = user.GetComponent<AttackComponent>();
        GuildMemberInfo memberInfo = new GuildMemberInfo
        {
          userId = user.Id,
          name = user.nickname,
          role = GuildRole.Member,
          joinTime = TimeInfo.Instance.ServerNow(),
          level = (int)attackComponent.level,
          attackNum = attackComponent.attackNum,
          offlineState = user.offlineState,
          lastActiveTime = TimeInfo.Instance.ServerNow(),
          contribution = ""
        };

        self.memberInfos.TryAdd(user.Id, memberInfo);

        // 更新用户公会信息
        user.userComInfo = new UserComInfo
        {
          commId = self.Id,
          commName = self.name
        };

        // 更新时间戳
        self.UpdateLastUpdateTime();

        // 通知所有公会成员
        self.SendMessageToAllMembers(new ServerGuildMemberJoinMsg
        {
          guildId = self.Id,
          memberInfo = memberInfo,
          joinMessage = $"{user.nickname} 加入了公会"
        });

        user.SendChat($"您已成功加入公会 {self.name}");
        return LogicRet.Success;
      }
      finally
      {
        self.memberInfosLock.ExitWriteLock();
      }
    }

    /// <summary>
    /// 移除公会成员
    /// </summary>
    public static LogicRet RemoveMember(this GuildInfo self, long userId, GuildOperationType operationType, string reason = "")
    {
      if (!self.memberInfos.TryGetValue(userId, out GuildMemberInfo memberInfo))
      {
        return LogicRet.Failed("用户不是公会成员");
      }

      // 会长不能被移除，只能转让或解散公会
      if (memberInfo.role == GuildRole.President)
      {
        return LogicRet.Failed("不能移除会长");
      }

      self.memberInfosLock.EnterWriteLock();
      try
      {
        self.memberInfos.TryRemove(userId, out _);

        // 清理用户公会信息
        User user = GlobalInfoCache.Instance.GetOnlineUser(userId);
        if (user != null)
        {
          user.userComInfo = null;
        }

        // 更新时间戳
        self.UpdateLastUpdateTime();

        // 通知所有公会成员
        string leaveMessage = operationType switch
        {
          GuildOperationType.Leave => $"{memberInfo.name} 离开了公会",
          GuildOperationType.Kicked => $"{memberInfo.name} 被踢出公会" + (string.IsNullOrEmpty(reason) ? "" : $"，原因：{reason}"),
          _ => $"{memberInfo.name} 离开了公会"
        };

        self.SendMessageToAllMembers(new ServerGuildMemberLeaveMsg
        {
          guildId = self.Id,
          userId = userId,
          userName = memberInfo.name,
          operationType = operationType,
          leaveMessage = leaveMessage
        });

        return LogicRet.Success;
      }
      finally
      {
        self.memberInfosLock.ExitWriteLock();
      }
    }

    /// <summary>
    /// 变更成员角色
    /// </summary>
    public static LogicRet ChangeMemberRole(this GuildInfo self, long userId, GuildRole newRole)
    {
      if (!self.memberInfos.TryGetValue(userId, out GuildMemberInfo memberInfo))
      {
        return LogicRet.Failed("用户不是公会成员");
      }

      if (memberInfo.role == newRole)
      {
        return LogicRet.Failed("用户已是该角色");
      }

      self.memberInfosLock.EnterWriteLock();
      try
      {
        GuildRole oldRole = memberInfo.role;
        memberInfo.role = newRole;
        // 更新时间戳
        self.UpdateLastUpdateTime();

        // 通知用户
        User user = GlobalInfoCache.Instance.GetOnlineUser(userId);
        user?.SendChat($"您的公会角色已变更为 {newRole.GetRoleDescription()}");

        // 通知所有公会成员
        self.SendMessageToAllMembers(new ServerUpdateGuildMsg
        {
          guildDaoInfo = self.ToDaoInfo()
        });

        ETLog.Info($"公会角色变更: 公会={self.name}, 用户={memberInfo.name}, {oldRole.GetRoleDescription()} -> {newRole.GetRoleDescription()}");

        return LogicRet.Success;
      }
      finally
      {
        self.memberInfosLock.ExitWriteLock();
      }
    }

    /// <summary>
    /// 转让会长职位
    /// </summary>
    public static LogicRet TransferLeadership(this GuildInfo self, long newLeaderId)
    {
      if (!self.memberInfos.TryGetValue(self.leaderId, out GuildMemberInfo oldLeaderInfo))
      {
        return LogicRet.Failed("当前会长信息不存在");
      }

      if (!self.memberInfos.TryGetValue(newLeaderId, out GuildMemberInfo newLeaderInfo))
      {
        return LogicRet.Failed("新会长不是公会成员");
      }

      self.memberInfosLock.EnterWriteLock();
      try
      {
        // 原会长变为副会长
        oldLeaderInfo.role = GuildRole.VicePresident;

        // 新会长
        newLeaderInfo.role = GuildRole.President;
        self.leaderId = newLeaderId;
        // 更新时间戳
        self.UpdateLastUpdateTime();

        // 通知相关用户
        User oldLeader = GlobalInfoCache.Instance.GetOnlineUser(oldLeaderInfo.userId);
        User newLeader = GlobalInfoCache.Instance.GetOnlineUser(newLeaderId);

        oldLeader?.SendChat($"您已将会长职位转让给 {newLeaderInfo.name}，您现在是副会长");
        newLeader?.SendChat($"您已成为公会 {self.name} 的新会长");

        // 通知所有公会成员
        self.SendMessageToAllMembers(new ServerUpdateGuildMsg
        {
          guildDaoInfo = self.ToDaoInfo()
        });

        ETLog.Info($"会长转让: 公会={self.name}, {oldLeaderInfo.name} -> {newLeaderInfo.name}");

        return LogicRet.Success;
      }
      finally
      {
        self.memberInfosLock.ExitWriteLock();
      }
    }

    /// <summary>
    /// 编辑公会信息
    /// </summary>
    public static LogicRet EditGuildInfo(this GuildInfo self, string guildName, string description, string announcement)
    {
      bool hasChanges = false;

      if (!string.IsNullOrEmpty(guildName) && guildName != self.name)
      {
        // 检查新名称是否已存在
        if (GlobalInfoCache.Instance.IsGuildNameExists(guildName))
        {
          return LogicRet.Failed("公会名称已存在");
        }

        self.name = guildName;
        hasChanges = true;

        // 更新所有成员的公会信息
        foreach (var memberKvp in self.memberInfos)
        {
          User member = GlobalInfoCache.Instance.GetOnlineUser(memberKvp.Key);
          if (member?.userComInfo != null)
          {
            member.userComInfo.commName = guildName;
          }
        }
      }

      if (!string.IsNullOrEmpty(description) && description != self.description)
      {
        if (description.Length > GuildManageComponent.GUILD_DESC_MAX_LENGTH)
        {
          return LogicRet.Failed($"公会描述长度不能超过{GuildManageComponent.GUILD_DESC_MAX_LENGTH}个字符");
        }
        self.description = description;
        hasChanges = true;
      }

      if (!string.IsNullOrEmpty(announcement) && announcement != self.announcement)
      {
        if (announcement.Length > GuildManageComponent.GUILD_ANNOUNCEMENT_MAX_LENGTH)
        {
          return LogicRet.Failed($"公会公告长度不能超过{GuildManageComponent.GUILD_ANNOUNCEMENT_MAX_LENGTH}个字符");
        }
        self.announcement = announcement;
        hasChanges = true;
      }

      if (hasChanges)
      {
        // 更新时间戳
        self.UpdateLastUpdateTime();

        // 通知所有公会成员
        self.SendMessageToAllMembers(new ServerUpdateGuildMsg
        {
          guildDaoInfo = self.ToDaoInfo()
        });
      }

      return LogicRet.Success;
    }

    /// <summary>
    /// 添加联盟
    /// </summary>
    public static LogicRet AddAlliance(this GuildInfo self, GuildInfo allianceGuild)
    {
      if (self.Id == allianceGuild.Id)
      {
        return LogicRet.Failed("不能与自己结盟");
      }

      if (self.allianceInfos.ContainsKey(allianceGuild.Id))
      {
        return LogicRet.Failed("已与该公会结盟");
      }

      self.allianceInfosLock.EnterWriteLock();
      try
      {
        long now = TimeInfo.Instance.ServerNow();

        // 添加到双方的联盟列表
        self.allianceInfos.TryAdd(allianceGuild.Id, new GuildAllianceInfo
        {
          guildId = allianceGuild.Id,
          guildName = allianceGuild.name,
          establishTime = now,
          status = GuildAllianceStatus.Established
        });

        allianceGuild.allianceInfos.TryAdd(self.Id, new GuildAllianceInfo
        {
          guildId = self.Id,
          guildName = self.name,
          establishTime = now,
          status = GuildAllianceStatus.Established
        });

        // 更新时间戳
        self.updateTime = now;
        self.lastUpdateTime = now;
        allianceGuild.updateTime = now;
        allianceGuild.lastUpdateTime = now;

        // 通知双方公会成员
        string allianceMessage = $"公会 {self.name} 与 {allianceGuild.name} 建立了联盟";
        self.SendMessageToAllMembers(new ServerUpdateGuildMsg { guildDaoInfo = self.ToDaoInfo() });
        allianceGuild.SendMessageToAllMembers(new ServerUpdateGuildMsg { guildDaoInfo = allianceGuild.ToDaoInfo() });

        foreach (var memberKvp in self.memberInfos)
        {
          User member = GlobalInfoCache.Instance.GetOnlineUser(memberKvp.Key);
          member?.SendChat(allianceMessage);
        }

        foreach (var memberKvp in allianceGuild.memberInfos)
        {
          User member = GlobalInfoCache.Instance.GetOnlineUser(memberKvp.Key);
          member?.SendChat(allianceMessage);
        }

        return LogicRet.Success;
      }
      finally
      {
        self.allianceInfosLock.ExitWriteLock();
      }
    }

    /// <summary>
    /// 移除联盟
    /// </summary>
    public static LogicRet RemoveAlliance(this GuildInfo self, long allianceGuildId)
    {
      if (!self.allianceInfos.TryGetValue(allianceGuildId, out GuildAllianceInfo allianceInfo))
      {
        return LogicRet.Failed("未与该公会结盟");
      }

      GuildInfo allianceGuild = GlobalInfoCache.Instance.GetGuild(allianceGuildId);
      if (allianceGuild == null)
      {
        // 如果联盟公会不存在，直接移除记录
        self.allianceInfos.TryRemove(allianceGuildId, out _);
        return LogicRet.Success;
      }

      self.allianceInfosLock.EnterWriteLock();
      try
      {
        // 从双方的联盟列表中移除
        self.allianceInfos.TryRemove(allianceGuildId, out _);
        allianceGuild.allianceInfos.TryRemove(self.Id, out _);

        long now = TimeInfo.Instance.ServerNow();
        // 更新时间戳
        self.updateTime = now;
        self.lastUpdateTime = now;
        allianceGuild.updateTime = now;
        allianceGuild.lastUpdateTime = now;

        // 通知双方公会成员
        string removeMessage = $"公会 {self.name} 与 {allianceGuild.name} 解除了联盟";
        self.SendMessageToAllMembers(new ServerUpdateGuildMsg { guildDaoInfo = self.ToDaoInfo() });
        allianceGuild.SendMessageToAllMembers(new ServerUpdateGuildMsg { guildDaoInfo = allianceGuild.ToDaoInfo() });

        foreach (var memberKvp in self.memberInfos)
        {
          User member = GlobalInfoCache.Instance.GetOnlineUser(memberKvp.Key);
          member?.SendChat(removeMessage);
        }

        foreach (var memberKvp in allianceGuild.memberInfos)
        {
          User member = GlobalInfoCache.Instance.GetOnlineUser(memberKvp.Key);
          member?.SendChat(removeMessage);
        }

        return LogicRet.Success;
      }
      finally
      {
        self.allianceInfosLock.ExitWriteLock();
      }
    }
  }
}
